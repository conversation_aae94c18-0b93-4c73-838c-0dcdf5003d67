package de.klosesoft.smartfinance.billing

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.*
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingClient.ProductType
import com.android.billingclient.api.QueryProductDetailsParams.Product
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import de.klosesoft.smartfinance.network.BackendApiFactory
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.SubscriptionRequestDto
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.SubscriptionResponseDto
import kotlinx.datetime.Clock

class BillingManager(private val context: Context) {
    companion object {
        const val PREMIUM_SUBSCRIPTION_ID = "de.klosesoft.finkimate.premium"
    }

    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private val _isPremium = MutableStateFlow(false)
    val isPremium: StateFlow<Boolean> = _isPremium

    private lateinit var billingClient: BillingClient
    private var productDetails: ProductDetails? = null

    init {
        setupBillingClient()
        // Load initial subscription status from backend
        coroutineScope.launch {
            _isPremium.value = getSavedPremiumStatusFromBackend()
        }
    }

    private fun setupBillingClient() {
        billingClient = BillingClient.newBuilder(context)
            .setListener { billingResult, purchases ->
                if (billingResult.responseCode == BillingResponseCode.OK && purchases != null) {
                    for (purchase in purchases) {
                        handlePurchase(purchase)
                    }
                }
            }
            .enablePendingPurchases()
            .build()

        billingClient.startConnection(object : BillingClientStateListener {
            override fun onBillingSetupFinished(billingResult: BillingResult) {
                if (billingResult.responseCode == BillingResponseCode.OK) {
                    querySubscriptions()
                    checkPremiumStatus()
                }
                println("Billing setup finished with response code: ${billingResult.responseCode}")
            }

            override fun onBillingServiceDisconnected() {
                // Try to reconnect
                billingClient.startConnection(this)
            }
        })
    }

    private fun querySubscriptions() {
        val queryProductDetailsParams = QueryProductDetailsParams.newBuilder()
            .setProductList(
                listOf(
                    Product.newBuilder()
                        .setProductId(PREMIUM_SUBSCRIPTION_ID)
                        .setProductType(ProductType.SUBS)
                        .build()
                )
            )
            .build()

        billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->
            if (billingResult.responseCode == BillingResponseCode.OK) {
                productDetailsList.firstOrNull()?.let {
                    productDetails = it
                }
            }
        }
    }

    fun launchBillingFlow(activity: Activity) {
        println("Launching billing flow with productDetails: $productDetails")
        productDetails?.let { details ->
            val offerToken = details.subscriptionOfferDetails?.firstOrNull()?.offerToken
            if (offerToken != null) {
                val billingFlowParams = BillingFlowParams.newBuilder()
                    .setProductDetailsParamsList(
                        listOf(
                            BillingFlowParams.ProductDetailsParams.newBuilder()
                                .setProductDetails(details)
                                .setOfferToken(offerToken)
                                .build()
                        )
                    )
                    .build()
                val billingResult = billingClient.launchBillingFlow(activity, billingFlowParams)
                println("Billing flow launched: $billingResult")
            }
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged) {
                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                    .build()
                billingClient.acknowledgePurchase(acknowledgePurchaseParams) { _ -> }
            }

            if (purchase.products.contains(PREMIUM_SUBSCRIPTION_ID)) {
                coroutineScope.launch {
                    _isPremium.value = true
                    savePremiumStatusToBackend(true)
                }
            }
        }
    }

    private fun checkPremiumStatus() {
        billingClient.queryPurchasesAsync(
            QueryPurchasesParams.newBuilder()
                .setProductType(ProductType.SUBS)
                .build()
        ) { billingResult, purchases ->
            if (billingResult.responseCode == BillingResponseCode.OK) {
                val hasPremium = purchases.any {
                    it.products.contains(PREMIUM_SUBSCRIPTION_ID) &&
                    it.purchaseState == Purchase.PurchaseState.PURCHASED
                }
                coroutineScope.launch {
                    _isPremium.value = hasPremium
                    savePremiumStatusToBackend(hasPremium)
                }
            } else {
                // Fallback to saved status from backend
                coroutineScope.launch {
                    _isPremium.value = getSavedPremiumStatusFromBackend()
                }
            }
        }
    }

    private suspend fun savePremiumStatusToBackend(isPremium: Boolean) {
        try {
            val subscriptionType = if (isPremium) {
                SubscriptionRequestDto.Type.PREMIUM
            } else {
                SubscriptionRequestDto.Type.FREE
            }

            val subscriptionRequest = SubscriptionRequestDto(
                type = subscriptionType,
                purchaseDate = if (isPremium) Clock.System.now() else null
            )

            BackendApiFactory.getSubscriptionApi().saveSubscription(subscriptionRequest)
            println("Successfully saved subscription status to backend: $isPremium")
        } catch (e: Exception) {
            println("Failed to save subscription status to backend: ${e.message}")
            // Could add local fallback here if needed
        }
    }

    private suspend fun getSavedPremiumStatusFromBackend(): Boolean {
        return try {
            val response = BackendApiFactory.getSubscriptionApi().getSubscription()
            val subscription = response.body()
            val isPremium = subscription.type == SubscriptionResponseDto.Type.PREMIUM &&
                           subscription.status == SubscriptionResponseDto.Status.ACTIVE
            println("Retrieved subscription status from backend: $isPremium")
            isPremium
        } catch (e: Exception) {
            println("Failed to retrieve subscription status from backend: ${e.message}")
            false // Default to free if backend is unavailable
        }
    }
}
