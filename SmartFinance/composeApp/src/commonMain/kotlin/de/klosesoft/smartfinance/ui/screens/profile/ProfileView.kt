package de.klosesoft.smartfinance.ui.screens.profile

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Logout
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material.icons.filled.Block
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Memory
import androidx.compose.material.icons.filled.NewReleases
import androidx.compose.material.icons.filled.Pending
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import co.touchlab.kermit.Logger
import de.comahe.i18n4k.createLocale
import de.comahe.i18n4k.language
import de.klosesoft.smartfinance.auth.AuthProviderFactory
import de.klosesoft.smartfinance.i18n.LocaleManager
import de.klosesoft.smartfinance.i18n.Messages
import de.klosesoft.smartfinance.network.BackendApiFactory
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.AiFeaturesStateResponseDto
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.UpdateUserProfileRequestDto
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.UserProfileDto
import de.klosesoft.smartfinance.ui.components.DialogPreferenceSelection
import de.klosesoft.smartfinance.billing.getSubscriptionManager
import io.ktor.client.request.delete
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileView(
    onBack: () -> Unit,
    onThemeChange: (ThemeMode) -> Unit,
    onLogout: () -> Unit,
    onNavigateToMemories: () -> Unit,
    currentThemeMode: ThemeMode = ThemeMode.SYSTEM,
) {
    val showThemeDialog = remember { mutableStateOf(false) }
    val showLanguageDialog = remember { mutableStateOf(false) }
    val showModelCardDialog = remember { mutableStateOf(false) }
    val showLogoutDialog = remember { mutableStateOf(false) }
    val showGenderDialog = remember { mutableStateOf(false) }
    val showAIDisabledDialog = remember { mutableStateOf(false) }
    val showDeleteAccountDialog = remember { mutableStateOf(false) }
    val showDangerousActionsExpanded = remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    var userProfile by remember { mutableStateOf<UserProfileDto?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var isEditing by remember { mutableStateOf(false) }
    var isSaving by remember { mutableStateOf(false) }

    // Subscription status
    val subscriptionManager = remember { getSubscriptionManager() }
    val isPremium by subscriptionManager.isPremium.collectAsState()

    // Editable fields
    var editedFirstName by remember { mutableStateOf("") }
    var editedCountry by remember { mutableStateOf("") }
    var editedYearOfBirth by remember { mutableStateOf("") }
    var editedGender by remember { mutableStateOf<UserProfileDto.Gender?>(null) }
    var editedMemoryActive by remember { mutableStateOf(false) }
    var editedLanguage by remember { mutableStateOf(UserProfileDto.Language.DE) }

    // AI function states
    var isTransactionCategorizationEnabled by remember { mutableStateOf(true) }
    var isAnomalyDetectionEnabled by remember { mutableStateOf(true) }
    var areTipsEnabled by remember { mutableStateOf(true) }
    var isChatEnabled by remember { mutableStateOf(true) }

    fun initEditFields() {
        userProfile?.let { profile ->
            editedFirstName = profile.firstName
            editedCountry = profile.country ?: ""
            editedYearOfBirth = profile.yearOfBirth?.toString() ?: ""
            editedGender = profile.gender
            editedMemoryActive = profile.memoryActive
            editedLanguage = UserProfileDto.Language.valueOf(profile.language.name)
        }
    }

    fun loadProfile() {
        isLoading = true
        coroutineScope.launch {
            try {
                val response = BackendApiFactory.getUserApi().getUserProfile()
                userProfile = response.body()

                initEditFields()

                isLoading = false
            } catch (e: Exception) {
                errorMessage =
                    Messages.profile_error_load.createString(e.message ?: "Unknown error")
                isLoading = false
            }
        }
    }

    fun saveProfile() {
        isSaving = true
        coroutineScope.launch {
            try {
                val updateRequest =
                    UpdateUserProfileRequestDto(
                        firstName = editedFirstName,
                        country = editedCountry.takeIf { it.isNotBlank() },
                        yearOfBirth = editedYearOfBirth.toIntOrNull(),
                        gender = editedGender?.let { mapGender(it) },
                        memoryActive = editedMemoryActive,
                        language = mapLanguage(editedLanguage),
                    )

                val response = BackendApiFactory.getUserApi().updateUserProfile(updateRequest)
                if (response.status in 200..299) {
                    userProfile = response.body()

                    initEditFields()
                    isEditing = false
                } else {
                    errorMessage = Messages.profile_error_save.createString(response.status)
                }
            } catch (e: Exception) {
                errorMessage =
                    Messages.profile_error_save.createString(e.message ?: "Unknown error")
            } finally {
                isSaving = false
            }
        }
    }

    fun loadAiFeaturesStates() {
        isLoading = true
        coroutineScope.launch {
            try {
                val response = BackendApiFactory.getUserApi().getAiFeaturesStates()
                val aiFeaturesStateResponseDto = response.body()

                aiFeaturesStateResponseDto.let {
                    isTransactionCategorizationEnabled =
                        aiFeaturesStateResponseDto.transactionCategorization
                    isAnomalyDetectionEnabled = aiFeaturesStateResponseDto.anomalyDetection
                    areTipsEnabled = aiFeaturesStateResponseDto.tips
                    isChatEnabled = aiFeaturesStateResponseDto.chat
                }

                isLoading = false
            } catch (e: Exception) {
                Logger.e("Error loading ai features states: ${e.message}", e)
                isLoading = false
            }
        }
    }

    fun saveAiFeaturesStates() {
        isSaving = true
        coroutineScope.launch {
            try {
                val aiFeaturesStateResponseDto =
                    AiFeaturesStateResponseDto(
                        transactionCategorization = isTransactionCategorizationEnabled,
                        anomalyDetection = isAnomalyDetectionEnabled,
                        tips = areTipsEnabled,
                        chat = isChatEnabled,
                    )

                BackendApiFactory.getUserApi().setAiFeaturesStates(aiFeaturesStateResponseDto)
                isSaving = false
            } catch (e: Exception) {
                Logger.e("Error saving ai features states: ${e.message}", e)
                isSaving = false
            }
        }
    }

    fun deleteAccount() {
        isSaving = true
        coroutineScope.launch {
            try {
                val userApi = BackendApiFactory.getUserApi()
//                val response = userApi.httpClient.delete("${userApi.baseUrl}/v1/user/account")

//                if (response.status.value in 200..299) {
//                    // Account deletion successful, logout the user
//                    AuthProviderFactory.getAuthProvider().logout()
//                    onLogout()
//                } else {
//                    errorMessage = "Failed to delete account. Status: ${response.status.value}"
//                }
            } catch (e: Exception) {
                errorMessage = "Failed to delete account: ${e.message ?: "Unknown error"}"
            } finally {
                isSaving = false
                showDeleteAccountDialog.value = false
            }
        }
    }

    LaunchedEffect(Unit) {
        loadProfile()
        loadAiFeaturesStates()
    }

    val currentThemeValue =
        when (currentThemeMode) {
            ThemeMode.SYSTEM -> Messages.app_theme_system.toString()
            ThemeMode.LIGHT -> Messages.app_theme_light.toString()
            ThemeMode.DARK -> Messages.app_theme_dark.toString()
        }

    if (showLogoutDialog.value) {
        AlertDialog(
            onDismissRequest = { showLogoutDialog.value = false },
            title = { Text(Messages.dialog_logout_title.toString()) },
            text = { Text(Messages.dialog_logout_message.toString()) },
            confirmButton = {
                TextButton(
                    onClick = {
                        coroutineScope.launch {
                            AuthProviderFactory.getAuthProvider().logout()
                            onLogout()
                        }
                    },
                ) {
                    Text(Messages.dialog_logout_confirm.toString())
                }
            },
            dismissButton = {
                TextButton(onClick = { showLogoutDialog.value = false }) {
                    Text(Messages.dialog_logout_cancel.toString())
                }
            },
        )
    }

    if (showDeleteAccountDialog.value) {
        AlertDialog(
            onDismissRequest = { showDeleteAccountDialog.value = false },
            title = { Text("Delete Account") },
            text = {
                Column {
                    Text("Are you sure you want to delete your account?")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "⚠️ All your data will be deleted permanently and cannot be recovered.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { deleteAccount() },
                    enabled = !isSaving
                ) {
                    if (isSaving) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text("Delete Account", color = MaterialTheme.colorScheme.error)
                    }
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteAccountDialog.value = false },
                    enabled = !isSaving
                ) {
                    Text("Cancel")
                }
            },
        )
    }

    if (showGenderDialog.value) {
        DialogPreferenceSelection(
            showDialog = true,
            title = Messages.profile_gender_select.toString(),
            currentValue = editedGender?.let { formatGender(it) } ?: "",
            labels =
                listOf(
                    Messages.profile_gender_male.toString(),
                    Messages.profile_gender_female.toString(),
                    Messages.profile_gender_diverse.toString(),
                ),
            onNegativeClick = { showGenderDialog.value = false },
        ) { selectedIndex ->
            editedGender =
                when (selectedIndex) {
                    0 -> UserProfileDto.Gender.MALE
                    1 -> UserProfileDto.Gender.FEMALE
                    2 -> UserProfileDto.Gender.OTHER
                    else -> null
                }
            showGenderDialog.value = false
        }
    }

    if (showLanguageDialog.value) {
        DialogPreferenceSelection(
            showDialog = true,
            title = Messages.profile_language.toString(),
            currentValue = formatLanguage(editedLanguage),
            labels =
                listOf(
                    Messages.profile_language_german.toString(),
                    Messages.profile_language_english.toString(),
                ),
            onNegativeClick = { showLanguageDialog.value = false },
        ) { selectedIndex ->

            editedLanguage =
                when (selectedIndex) {
                    0 -> UserProfileDto.Language.DE
                    1 -> UserProfileDto.Language.EN
                    else -> UserProfileDto.Language.DE
                }

            Logger.i { "Selected language: ${editedLanguage.name}" }
            val locale = createLocale(editedLanguage.name.lowercase())

            Logger.i { "Setting locale to: ${locale.language}" }
            LocaleManager.setLocale(locale)

            showLanguageDialog.value = false
        }
    }

    if (showAIDisabledDialog.value) {
        AlertDialog(
            onDismissRequest = { showAIDisabledDialog.value = false },
            title = { Text(Messages.ai_opt_out.toString()) },
            text = {
                Column {
                    Text(
                        "Hinweis: Das Deaktivieren von KI-Funktionen kann die Nützlichkeit und Funktionalität der App einschränken.",
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "Transaktionskategorisierung",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.weight(1f),
                        )
                        Switch(
                            checked = isTransactionCategorizationEnabled,
                            onCheckedChange = { isTransactionCategorizationEnabled = it },
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "Anomalieerkennung",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.weight(1f),
                        )
                        Switch(
                            checked = isAnomalyDetectionEnabled,
                            onCheckedChange = { isAnomalyDetectionEnabled = it },
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "Tipps",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.weight(1f),
                        )
                        Switch(
                            checked = areTipsEnabled,
                            onCheckedChange = { areTipsEnabled = it },
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            text = "Chat",
                            style = MaterialTheme.typography.bodyLarge,
                            modifier = Modifier.weight(1f),
                        )
                        Switch(
                            checked = isChatEnabled,
                            onCheckedChange = { isChatEnabled = it },
                        )
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    saveAiFeaturesStates()
                    showAIDisabledDialog.value = false
                }) {
                    Text(Messages.app_button_ok.toString())
                }
            },
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(userProfile?.firstName ?: Messages.nav_profile.toString()) },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = Messages.app_back.toString(),
                        )
                    }
                },
                actions = {
                    if (isEditing) {
                        IconButton(
                            onClick = {
                                if (userProfile != null) {
                                    editedFirstName = userProfile!!.firstName
                                    editedCountry = userProfile!!.country ?: ""
                                    editedYearOfBirth = userProfile!!.yearOfBirth?.toString() ?: ""
                                    editedGender = userProfile!!.gender
                                    editedMemoryActive = userProfile!!.memoryActive
                                    editedLanguage =
                                        UserProfileDto.Language.valueOf(userProfile!!.language.name)
                                }
                                isEditing = false
                            },
                            enabled = !isSaving,
                        ) {
                            Icon(
                                Icons.Default.Close,
                                contentDescription = Messages.app_cancel.toString(),
                            )
                        }
                        IconButton(
                            onClick = { saveProfile() },
                            enabled = !isSaving,
                        ) {
                            if (isSaving) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp,
                                )
                            } else {
                                Icon(
                                    Icons.Default.Save,
                                    contentDescription = Messages.app_save.toString(),
                                )
                            }
                        }
                    } else {
                        IconButton(onClick = { isEditing = true }) {
                            Icon(
                                Icons.Default.Edit,
                                contentDescription = Messages.app_edit.toString(),
                            )
                        }
                    }
                },
                modifier = Modifier.padding(top = 12.dp),
            )
        },
    ) { paddingValues ->
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize().padding(paddingValues),
                contentAlignment = Alignment.Center,
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
            ) {
                // User Profile Section
                Card(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                    ) {
                        if (isEditing) {
                            // Editable fields
                            OutlinedTextField(
                                value = editedFirstName,
                                onValueChange = { editedFirstName = it },
                                label = { Text(Messages.profile_name.toString()) },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true,
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            OutlinedTextField(
                                value = editedCountry,
                                onValueChange = { editedCountry = it },
                                label = { Text(Messages.profile_country.toString()) },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true,
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            OutlinedTextField(
                                value = editedYearOfBirth,
                                onValueChange = {
                                    // Only allow digits
                                    if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                        editedYearOfBirth = it
                                    }
                                },
                                label = { Text(Messages.profile_birth_year.toString()) },
                                modifier = Modifier.fillMaxWidth(),
                                singleLine = true,
                                keyboardOptions =
                                    KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                    ),
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            OutlinedTextField(
                                value = editedGender?.let { formatGender(it) } ?: "",
                                onValueChange = { },
                                label = { Text(Messages.profile_gender.toString()) },
                                modifier = Modifier.fillMaxWidth(),
                                readOnly = true,
                                trailingIcon = {
                                    IconButton(onClick = { showGenderDialog.value = true }) {
                                        Icon(
                                            Icons.Default.ArrowDropDown,
                                            Messages.profile_gender_select.toString(),
                                        )
                                    }
                                },
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                            ) {
                                Text(
                                    text = "${Messages.profile_memory} ${Messages.profile_activated}",
                                    style = MaterialTheme.typography.bodyLarge,
                                    modifier = Modifier.weight(1f),
                                )
                                Switch(
                                    checked = editedMemoryActive,
                                    onCheckedChange = { editedMemoryActive = it },
                                )
                            }

                            OutlinedTextField(
                                value =
                                    editedLanguage.let {
                                        LocaleManager.getLocaleName(
                                            createLocale(it.name.lowercase()),
                                        )
                                    },
                                onValueChange = { },
                                label = { Text(Messages.profile_language.toString()) },
                                modifier = Modifier.fillMaxWidth(),
                                readOnly = true,
                                trailingIcon = {
                                    IconButton(onClick = { showLanguageDialog.value = true }) {
                                        Icon(
                                            Icons.Default.ArrowDropDown,
                                            Messages.profile_select.toString(),
                                        )
                                    }
                                },
                            )
                        } else {
                            // Display-only fields
                            userProfile?.let { profile ->
                                Text(
                                    text = Messages.profile_details.toString(),
                                    style = MaterialTheme.typography.headlineMedium,
                                )
                                Spacer(modifier = Modifier.height(8.dp))

                                // Status-Anzeige mit Icon
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth(),
                                ) {
                                    val (statusIcon, statusColor) = getStatusIcon(profile.status)
                                    Icon(
                                        imageVector = statusIcon,
                                        contentDescription =
                                            Messages.profile_status.createString(
                                                "",
                                            ),
                                        tint = statusColor,
                                        modifier = Modifier.size(20.dp),
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text =
                                            Messages.profile_status.createString(
                                                formatStatus(
                                                    profile.status,
                                                ),
                                            ),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = statusColor,
                                    )
                                }

                                // Subscription Status
                                Spacer(modifier = Modifier.height(8.dp))
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.fillMaxWidth(),
                                ) {
                                    val (subscriptionIcon, subscriptionColor) = if (isPremium) {
                                        Pair(Icons.Default.Star, Color(0xFFFFD700)) // Gold color for premium
                                    } else {
                                        Pair(Icons.Default.Star, Color.Gray) // Gray for free
                                    }
                                    Icon(
                                        imageVector = subscriptionIcon,
                                        contentDescription = "Subscription Status",
                                        tint = subscriptionColor,
                                        modifier = Modifier.size(20.dp),
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = if (isPremium) "Premium Subscription" else "Free Plan",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = subscriptionColor,
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))
                                Text(
                                    text = profile.email,
                                    style = MaterialTheme.typography.bodyLarge,
                                )

                                profile.country?.let {
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = "${Messages.profile_country}: $it",
                                        style = MaterialTheme.typography.bodyMedium,
                                    )
                                }

                                profile.yearOfBirth?.let {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "${Messages.profile_birth_year}: $it",
                                        style = MaterialTheme.typography.bodyMedium,
                                    )
                                }

                                profile.gender?.let {
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "${Messages.profile_gender}: ${formatGender(it)}",
                                        style = MaterialTheme.typography.bodyMedium,
                                    )
                                }

                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = "${Messages.profile_memory}: ${if (profile.memoryActive) {
                                        Messages.profile_activated
                                            .toString()
                                    } else {
                                        Messages.profile_deactivated.toString()
                                    }}",
                                    style = MaterialTheme.typography.bodyMedium,
                                )

                                Spacer(modifier = Modifier.height(4.dp))
                                Text(
                                    text = "${Messages.profile_language}: ${
                                        LocaleManager.getLocaleName(
                                            createLocale(profile.language.name.lowercase()),
                                        )
                                    }",
                                    style = MaterialTheme.typography.bodyMedium,
                                )
                            } ?: run {
                                Text(
                                    text = Messages.profile_no_data.toString(),
                                    style = MaterialTheme.typography.bodyLarge,
                                )
                            }
                        }
                    }
                }

                if (!isEditing) {
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // General Settings Section
                    Text(
                        text = Messages.profile_settings.toString(),
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    )

                    PreferenceItem(
                        icon = Icons.Default.DarkMode,
                        title = Messages.app_theme.toString(),
                        subtitle = currentThemeValue,
                        onClick = { showThemeDialog.value = true },
                    )

                    HorizontalDivider()

                    PreferenceItem(
                        icon = Icons.Default.Memory,
                        title = Messages.profile_memory.toString(),
                        onClick = onNavigateToMemories,
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // AI & Features Section
                    Text(
                        text = "AI & Features",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    )

                    PreferenceItem(
                        icon = Icons.Default.Info,
                        title = "Verwendetes LLM (Model Card)",
                        onClick = { showModelCardDialog.value = true },
                        tint = MaterialTheme.colorScheme.primary,
                    )

                    HorizontalDivider()

                    PreferenceItem(
                        icon = Icons.Default.Block,
                        title = "KI Funktionen deaktivieren",
                        onClick = { showAIDisabledDialog.value = true },
                        tint = MaterialTheme.colorScheme.error,
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // Account Actions Section
                    Text(
                        text = "Account",
                        style = MaterialTheme.typography.titleMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    )

                    PreferenceItem(
                        icon = Icons.AutoMirrored.Filled.Logout,
                        title = Messages.profile_logout.toString(),
                        onClick = { showLogoutDialog.value = true },
                        tint = MaterialTheme.colorScheme.error,
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // Dangerous Actions Section
                    Text(
                        text = "⚠️ Dangerous Actions",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.error,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                    )

                    PreferenceItem(
                        icon = Icons.Default.Delete,
                        title = "Delete Account",
                        onClick = { showDeleteAccountDialog.value = true },
                        tint = MaterialTheme.colorScheme.error,
                    )
                }
            }
        }

        DialogPreferenceSelection(
            showDialog = showThemeDialog.value,
            title = Messages.profile_theme.toString(),
            currentValue = currentThemeValue,
            labels =
                listOf(
                    Messages.app_theme_system.toString(),
                    Messages.app_theme_light.toString(),
                    Messages.app_theme_dark.toString(),
                ),
            onNegativeClick = { showThemeDialog.value = false },
        ) { selectedIndex ->
            val selectedTheme =
                when (selectedIndex) {
                    0 -> ThemeMode.SYSTEM
                    1 -> ThemeMode.LIGHT
                    2 -> ThemeMode.DARK
                    else -> ThemeMode.SYSTEM
                }
            onThemeChange(selectedTheme)
            showThemeDialog.value = false
        }

        if (showModelCardDialog.value) {
            AlertDialog(
                onDismissRequest = { showModelCardDialog.value = false },
                title = { Text(Messages.model_card_title.toString()) },
                text = {
                    Column {
                        Text(Messages.model_card_description.toString())
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(Messages.model_card_training_data.toString())
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(Messages.model_card_use_cases.toString())
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(Messages.model_card_limitations.toString())
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(Messages.model_card_ethical_considerations.toString())
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showModelCardDialog.value = false }) {
                        Text(Messages.app_button_ok.toString())
                    }
                },
            )
        }
    }

    errorMessage?.let { message ->
        AlertDialog(
            onDismissRequest = { errorMessage = null },
            title = { Text(Messages.error_title.toString()) },
            text = { Text(message) },
            confirmButton = {
                TextButton(onClick = { errorMessage = null }) {
                    Text(Messages.error_ok.toString())
                }
            },
        )
    }
}

private fun formatGender(gender: UserProfileDto.Gender): String =
    when (gender) {
        UserProfileDto.Gender.MALE -> Messages.profile_gender_male.toString()
        UserProfileDto.Gender.FEMALE -> Messages.profile_gender_female.toString()
        UserProfileDto.Gender.OTHER -> Messages.profile_gender_diverse.toString()
    }

private fun mapGender(gender: UserProfileDto.Gender): UpdateUserProfileRequestDto.Gender =
    UpdateUserProfileRequestDto.Gender.valueOf(gender.name)

private fun formatLanguage(language: UserProfileDto.Language): String =
    when (language) {
        UserProfileDto.Language.DE -> Messages.profile_language_german.toString()
        UserProfileDto.Language.EN -> Messages.profile_language_english.toString()
    }

private fun mapLanguage(language: UserProfileDto.Language): UpdateUserProfileRequestDto.Language =
    UpdateUserProfileRequestDto.Language.valueOf(language.name)

enum class ThemeMode {
    LIGHT,
    DARK,
    SYSTEM,
}

// Funktion zum Abrufen des passenden Icons für den Status
@Composable
private fun getStatusIcon(status: UserProfileDto.Status?): Pair<ImageVector, Color> =
    when (status) {
        UserProfileDto.Status.ACTIVE ->
            Pair(
                Icons.Default.CheckCircle,
                Color(0xFF4CAF50), // Grün
            )

        UserProfileDto.Status.INACTIVE ->
            Pair(
                Icons.Default.Cancel,
                Color(0xFFFF9800), // Orange
            )

        UserProfileDto.Status.PENDING ->
            Pair(
                Icons.Default.Pending,
                Color(0xFF2196F3), // Blau
            )

        UserProfileDto.Status.BLOCKED ->
            Pair(
                Icons.Default.Block,
                Color(0xFFD32F2F), // Rot
            )

        UserProfileDto.Status.INIT ->
            Pair(
                Icons.Default.NewReleases,
                Color(0xFF607D8B),
            )

        UserProfileDto.Status.ONBOARDING ->
            Pair(
                Icons.Default.NewReleases,
                Color(0xFF607D8B),
            )

        null ->
            Pair(
                Icons.Default.Cancel,
                Color.Gray,
            )
    }

private fun formatStatus(status: UserProfileDto.Status): String =
    when (status) {
        UserProfileDto.Status.ONBOARDING -> Messages.profile_status_onboarding.toString()
        UserProfileDto.Status.INIT -> Messages.profile_status_init.toString()
        UserProfileDto.Status.ACTIVE -> Messages.profile_status_active.toString()
        UserProfileDto.Status.INACTIVE -> Messages.profile_status_inactive.toString()
        UserProfileDto.Status.PENDING -> Messages.profile_status_pending.toString()
        UserProfileDto.Status.BLOCKED -> Messages.profile_status_blocked.toString()
    }
