package de.klosesoft.smartfinance.network

import co.touchlab.kermit.Logger
import de.klosesoft.smartfinance.PlatformName
import de.klosesoft.smartfinance.auth.AuthProviderFactory
import de.klosesoft.smartfinance.config.ConfigurationManager
import de.klosesoft.smartfinance.getPlatform
import de.klosesoft.smartfinance.httpClient
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AccountAdditionApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AccountsApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AdvicesApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AnomaliesApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AssistantApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.AuthApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.ContractsApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.ExpensesApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.FinancialStatusApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.InvoicesApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.MemoriesApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.OnboardingApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.PaymentOrdersApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.PushNotificationsApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.SavingsGoalsApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.SubscriptionApi
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.UserApi
import de.klosesoft.smartfinance.platform.SsePlatformClient
import io.ktor.client.HttpClient
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.HttpCallValidator
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object BackendApiFactory {
    private lateinit var baseUrl: String

    private var sharedHttpClient: HttpClient? = null

    fun initialize(ssePlatformClient: SsePlatformClient) {
        baseUrl =
            when (getPlatform().name) {
                PlatformName.WEB -> ConfigurationManager.getConfig().api.baseUrlWeb
                PlatformName.IOS -> ConfigurationManager.getConfig().api.baseUrlIos
                PlatformName.ANDROID -> ConfigurationManager.getConfig().api.baseUrlAndroid
                else -> ConfigurationManager.getConfig().api.baseUrlAndroid
            }

        Logger.i { "BackendApiFactory initialized with platform: ${getPlatform().name}" }
        Logger.i { "Using baseUrl: $baseUrl" }

        SseManager.initialize(baseUrl, ssePlatformClient)
    }

    var onAuthenticationFailed: () -> Unit = {}

    private fun httpClient(): HttpClient {
        if (sharedHttpClient == null) {
            sharedHttpClient =
                httpClient {
                    install(ContentNegotiation) {
                        json()
                    }

                    val authProvider = AuthProviderFactory.getAuthProvider()
                    authProvider.getCurrentToken()?.let {
                        install(DefaultRequest) {
                            headers.append(HttpHeaders.Authorization, "Bearer $it")
                        }
                    }

                    install(HttpCallValidator) {
                        validateResponse { response ->
                            if (response.status == HttpStatusCode.Unauthorized) {
                                Logger.i {
                                    "Unauthorized response received, attempting token refresh"
                                }

                                val refreshResult = authProvider.refreshToken()

                                if (refreshResult.isFailure) {
                                    Logger.i {
                                        "Token refresh failed, clearing token and triggering callback"
                                    }
                                    authProvider.clearToken()
                                    MainScope().launch {
                                        onAuthenticationFailed()
                                    }
                                    throw ClientRequestException(response, "Unauthorized")
                                } else {
                                    // Token refreshed successfully, but we still need to fail this request
                                    // so the caller can retry with the new token
                                    throw ClientRequestException(
                                        response,
                                        "Token refreshed, please retry",
                                    )
                                }
                            }
                        }
                    }
                }
        }
        return sharedHttpClient!!
    }

    fun getAccountAdditionApi(): AccountAdditionApi =
        AccountAdditionApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getAccountsApi(): AccountsApi =
        AccountsApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getAssistantApi(): AssistantApi =
        AssistantApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getAdvicesApi(): AdvicesApi =
        AdvicesApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getContractsApi(): ContractsApi =
        ContractsApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getInvoicesApi(): InvoicesApi =
        InvoicesApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getPaymentOrdersApi(): PaymentOrdersApi =
        PaymentOrdersApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getUserApi(): UserApi =
        UserApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getAuthApi(): AuthApi =
        AuthApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getSavingsGoalsApi(): SavingsGoalsApi =
        SavingsGoalsApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getExpensesApi(): ExpensesApi =
        ExpensesApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun memoriesApi(): MemoriesApi =
        MemoriesApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getAnomaliesApi(): AnomaliesApi =
        AnomaliesApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getFinancialStatusApi(): FinancialStatusApi =
        FinancialStatusApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getOnboardingApi(): OnboardingApi =
        OnboardingApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getPushNotificationsApi(): PushNotificationsApi =
        PushNotificationsApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )

    fun getSubscriptionApi(): SubscriptionApi =
        SubscriptionApi(
            baseUrl = baseUrl,
            httpClient = httpClient(),
        )
    fun resetHttpClient() {
        sharedHttpClient?.close()
        sharedHttpClient = null
    }
}
