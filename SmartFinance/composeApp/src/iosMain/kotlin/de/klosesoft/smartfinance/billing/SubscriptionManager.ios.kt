package de.klosesoft.smartfinance.billing

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import de.klosesoft.smartfinance.network.BackendApiFactory
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.SubscriptionRequestDto
import de.klosesoft.smartfinance.network.generated.api.smartfinance.client.model.SubscriptionResponseDto
import kotlinx.datetime.Clock

class IOSSubscriptionManager : SubscriptionStatus {
    private val coroutineScope = CoroutineScope(Dispatchers.Main)
    private val _isPremium = MutableStateFlow(false)
    override val isPremium: StateFlow<Boolean> = _isPremium

    init {
        // Load initial subscription status from backend
        coroutineScope.launch {
            _isPremium.value = getSavedPremiumStatusFromBackend()
        }
    }

    fun updatePremiumStatus(isPremium: Boolean) {
        _isPremium.value = isPremium
        // Also save to backend
        coroutineScope.launch {
            savePremiumStatusToBackend(isPremium)
        }
    }

    private suspend fun savePremiumStatusToBackend(isPremium: Boolean) {
        try {
            val subscriptionType = if (isPremium) {
                SubscriptionRequestDto.Type.PREMIUM
            } else {
                SubscriptionRequestDto.Type.FREE
            }

            val subscriptionRequest = SubscriptionRequestDto(
                type = subscriptionType,
                purchaseDate = if (isPremium) Clock.System.now() else null
            )

            BackendApiFactory.getSubscriptionApi().saveSubscription(subscriptionRequest)
            println("Successfully saved subscription status to backend: $isPremium")
        } catch (e: Exception) {
            println("Failed to save subscription status to backend: ${e.message}")
            // Could add local fallback here if needed
        }
    }

    private suspend fun getSavedPremiumStatusFromBackend(): Boolean {
        return try {
            val response = BackendApiFactory.getSubscriptionApi().getSubscription()
            val subscription = response.body()
            val isPremium = subscription?.type == SubscriptionResponseDto.Type.PREMIUM &&
                           subscription.status == SubscriptionResponseDto.Status.ACTIVE
            println("Retrieved subscription status from backend: $isPremium")
            isPremium
        } catch (e: Exception) {
            println("Failed to retrieve subscription status from backend: ${e.message}")
            false // Default to free if backend is unavailable
        }
    }
}

private val subscriptionManager = IOSSubscriptionManager()

actual fun getSubscriptionManager(): SubscriptionStatus = subscriptionManager

// Function to be called from Swift
@Suppress("unused")
fun updatePremiumStatus(isPremium: Boolean) {
    subscriptionManager.updatePremiumStatus(isPremium)
}
